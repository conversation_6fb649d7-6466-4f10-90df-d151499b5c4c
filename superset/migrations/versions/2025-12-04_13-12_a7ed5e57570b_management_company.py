# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""Management company

Revision ID: a7ed5e57570b
Revises: b9d3a202f14a
Create Date: 2025-12-04 13:12:18.766385

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session

revision = "a7ed5e57570b"
down_revision = "b9d3a202f14a"

Base = declarative_base()


class UserInfo(Base):
    """Local UserInfo model for migration"""

    __tablename__ = "user_info"

    id = sa.Column(sa.Integer, primary_key=True)
    user_id = sa.Column(sa.Integer, sa.ForeignKey("ab_user.id"))
    is_management = sa.Column(sa.Boolean, nullable=False, default=False)


class User(Base):
    """Local User model for migration"""

    __tablename__ = "ab_user"

    id = sa.Column(sa.Integer, primary_key=True)
    email = sa.Column(sa.String)


def upgrade():
    # Add is_management column to user_info table
    op.add_column(
        "user_info",
        sa.Column(
            "is_management", sa.Boolean(), nullable=False, server_default="false"
        ),
    )

    # Set is_management to true for users with matching email patterns
    bind = op.get_bind()
    session = Session(bind=bind)

    try:
        # Find users with matching email patterns
        users = (
            session.query(User)
            .filter(
                sa.or_(
                    User.email.ilike("%@dodopizza.%"),
                    User.email.ilike("%@dodobrands.io"),
                )
            )
            .all()
        )

        user_ids = [user.id for user in users]
        print(f"Found {len(user_ids)} management company users")
        if user_ids:
            # Update user_info records for matching users
            (
                session.query(UserInfo)
                .filter(UserInfo.user_id.in_(user_ids))
                .update({"is_management": True}, synchronize_session=False)
            )
            session.commit()
    finally:
        session.close()


def downgrade():
    op.drop_column("user_info", "is_management")

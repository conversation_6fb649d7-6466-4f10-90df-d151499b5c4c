# DODO added #32839638
from __future__ import annotations

import logging
import re
from datetime import datetime
from typing import Any

import pycountry
from flask_appbuilder.security.sqla.models import User
from sqlalchemy.exc import SQLAlchemyError

from superset.daos.base import <PERSON>DA<PERSON>
from superset.extensions import db, security_manager
from superset.models.user_info import UserInfo
from superset.utils.core import get_user_id

logger = logging.getLogger(__name__)


def _is_management_email(email: str | None) -> bool:
    """Check if email matches management company patterns.

    Args:
        email: User's email address

    Returns:
        bool: True if email matches management patterns, False otherwise
    """
    if not email:
        return False
    email_lower = email.lower()
    # Match: %@dodopizza.% or %@dodobrands.io
    pattern = r".*@dodopizza\..*|.*@dodobrands\.io$"
    return bool(re.match(pattern, email_lower))


class UserInfoDAO(BaseDAO[UserInfo]):
    # base_filter = DashboardAccessFilter
    @staticmethod
    def get_onboarding() -> dict[str, Any]:
        user_id = get_user_id()
        try:
            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            return user_info.__dict__
        except Exception:  # pylint: disable=broad-except
            logger.warning(
                "User id = %s dont have onboarding info in database", user_id
            )
            return {"onboarding_started_time": None, "is_onboarding_finished": False}

    @classmethod
    def get_by_user_id(cls, user_id: int) -> UserInfo:
        try:
            query = db.session.query(UserInfo).filter(UserInfo.user_id == user_id)
            user_info = query.one_or_none()
            if not user_info:
                UserInfoDAO.create_userinfo(user_id, "ru")
                cls.get_by_user_id(user_id)
        except AttributeError:
            UserInfoDAO.create_userinfo(user_id, "ru")
            cls.get_by_user_id(user_id)
        return user_info

    @staticmethod
    def create_onboarding(
        dodo_role: str, started_time: datetime
    ) -> bool:  # DODO changed #33835937
        """Create onboarding record for user.

        Args:
            dodo_role (str): User's role in Dodo
            started_time (datetime.datetime): Onboarding start time

        Returns:
            bool: True if successful, False otherwise
        """
        user_id = get_user_id()
        if not user_id:
            logger.warning("Cannot create onboarding - user ID not found")
            return False

        try:
            user = security_manager.get_user_by_id(user_id)
            is_management = _is_management_email(user.email if user else None)

            user_info = UserInfo(
                user_id=user_id,
                dodo_role=dodo_role,
                onboarding_started_time=started_time,
                is_management=is_management,
            )
            db.session.add(user_info)
            db.session.commit()  # pylint: disable=consider-using-transaction
            return True

        except SQLAlchemyError as ex:
            logger.error("Database error creating onboarding: %s", str(ex))
            db.session.rollback()  # pylint: disable=consider-using-transaction
            return False
        except Exception as ex:  # pylint: disable=broad-except
            logger.error("Failed to create onboarding: %s", str(ex))
            return False

    @staticmethod
    def update_onboarding(dodo_role: str, started_time: datetime) -> dict[str, Any]:
        user_id = get_user_id()
        try:
            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            user_info.dodo_role = dodo_role
            user_info.onboarding_started_time = started_time
            db.session.commit()  # pylint: disable=consider-using-transaction
        except AttributeError:
            UserInfoDAO.create_onboarding(dodo_role, started_time)

        return {"dodo_role": dodo_role, "onboarding_started_time": started_time}

    @staticmethod
    def finish_onboarding() -> bool:
        """Mark user's onboarding as finished.

        Returns:
            bool: Status of onboarding completion
        """
        user_id = get_user_id()
        if not user_id:
            logger.warning("Cannot finish onboarding - user ID not found")
            return False

        try:
            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            if not user_info:
                logger.warning("User info not found for user %s", user_id)
                return False

            user_info.is_onboarding_finished = True
            db.session.merge(user_info)
            db.session.commit()  # pylint: disable=consider-using-transaction
            return True
        except SQLAlchemyError as ex:
            logger.error("Database error finishing onboarding: %s", str(ex))
            db.session.rollback()  # pylint: disable=consider-using-transaction
            return False

    @staticmethod
    def create_userinfo(user_id: int, lang: str) -> bool:  # DODO changed #33835937
        """Create user info record.

        Args:
            user_id (int): User's ID
            lang (str): User's language preference

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not user_id:
                logger.warning("Cannot create user info - user ID not found")
                return False

            user = security_manager.get_user_by_id(user_id)
            is_management = _is_management_email(user.email if user else None)

            model = UserInfo()
            model.language = lang
            model.user_id = user_id
            model.is_management = is_management
            try:
                db.session.add(model)
                db.session.commit()  # pylint: disable=consider-using-transaction
                return True
            except SQLAlchemyError as ex:
                logger.error("Database error creating user info: %s", str(ex))
                db.session.rollback()  # pylint: disable=consider-using-transaction
                return False
        except Exception as ex:  # pylint: disable=broad-except
            logger.error("Failed to create user info: %s", str(ex))
            return False

    @staticmethod
    def update_language(lang: str) -> None:  # DODO changed #33835937
        try:
            user_id = get_user_id()
            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            user_info.language = lang
            db.session.commit()  # pylint: disable=consider-using-transaction
        except AttributeError:
            user_id = get_user_id()
            UserInfoDAO.create_userinfo(user_id, lang)

    @staticmethod
    def update_dodo_role(user_id: int, dodo_role: str) -> bool:  # DODO added
        """Update user's DODO role by user ID.

        Args:
            user_id (int): ID of the user to update
            dodo_role (str): User's role in DODO

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not user_id:
                logger.warning("Cannot update dodo role - user ID not provided")
                return False

            user_info = UserInfoDAO.get_by_user_id(user_id)

            user_info.dodo_role = dodo_role
            db.session.commit()  # pylint: disable=consider-using-transaction
            return True
        except Exception as ex:  # pylint: disable=broad-except
            logger.error("Failed to update dodo role: %s", str(ex))
            db.session.rollback()  # pylint: disable=consider-using-transaction
            return False

    @staticmethod
    def get_country_by_user_id() -> list[UserInfo]:
        user_id = get_user_id()
        try:
            user = (
                db.session.query(security_manager.user_model)
                .filter(security_manager.user_model.id == user_id)
                .one_or_none()
            )
            return user.user_info
        except Exception:  # pylint: disable=broad-except
            return []

    @staticmethod
    def get_dodo_role(user_id: int) -> str:  # DODO changed #33835937
        """Get dodo role for a user by their ID.

        Args:
            user_id: The ID of the user

        Returns:
            str: The dodo role of the user or empty string if not found/error
        """
        dodo_role = ""
        try:
            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            if user_info and user_info.dodo_role:
                dodo_role = user_info.dodo_role
        except SQLAlchemyError:
            logger.warning("Exception when select dodo_role from db")
        except AttributeError:
            logger.warning("User id = %s dont have dodo_role in database", user_id)
        except Exception:  # pylint: disable=broad-except
            logger.exception("Error get dodo_role")

        return dodo_role

    @staticmethod
    def insert_country(country_iso_num: int, username: str) -> None:
        """Write user's country to database.

        Args:
            country_iso_num (int): Country ISO numeric code
            username (str): Username of the user
        """
        try:
            # Format country ISO number to 3 digits with leading zeros
            country_iso_str = f"{country_iso_num:03d}"
            country = pycountry.countries.get(numeric=country_iso_str)
            if not country:
                logger.warning(
                    "Country not found for ISO numeric code: %s", country_iso_num
                )
                return

            user = (
                db.session.query(User).filter(User.username == username).one_or_none()
            )
            if not user:
                logger.warning("User not found: %s", username)
                return

            user_id = user.id

            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            if not user_info:
                UserInfoDAO.create_userinfo(user_id, "ru")
                user_info = (
                    db.session.query(UserInfo)
                    .filter(UserInfo.user_id == user_id)
                    .one_or_none()
                )

            if user_info:
                user_info.country_name = country.name
                user_info.country_num = country_iso_num
                db.session.commit()  # pylint: disable=consider-using-transaction
        except SQLAlchemyError:
            logger.warning("Error select when insert country in db")
            db.session.rollback()  # pylint: disable=consider-using-transaction
        except AttributeError:
            logger.warning("Error add to db country")
        except Exception as ex:  # pylint: disable=broad-except
            logger.exception("Unexpected error in insert_country: %s", ex)
            db.session.rollback()  # pylint: disable=consider-using-transaction

    @staticmethod
    def insert_data_auth(data_auth: str, username: str) -> None:
        """Write authentication data from DodoIs to database.

        Args:
            data_auth (str): Authentication data from DodoIs
            username (str): Username of the user
        """
        try:
            user = (
                db.session.query(User).filter(User.username == username).one_or_none()
            )
            if not user:
                logger.warning("User not found: %s", username)
                return

            user_id = user.id

            user_info = (
                db.session.query(UserInfo)
                .filter(UserInfo.user_id == user_id)
                .one_or_none()
            )
            if not user_info:
                UserInfoDAO.create_userinfo(user_id, "ru")
                user_info = (
                    db.session.query(UserInfo)
                    .filter(UserInfo.user_id == user_id)
                    .one_or_none()
                )

            if user_info:
                user_info.data_auth_dodo = data_auth
                db.session.commit()  # pylint: disable=consider-using-transaction
        except SQLAlchemyError:
            logger.warning("Error select when insert data auth in db")
            db.session.rollback()  # pylint: disable=consider-using-transaction
        except AttributeError:
            logger.warning("Error add to db data_auth_dodo")
        except Exception as ex:  # pylint: disable=broad-except
            logger.exception("Unexpected error in insert_data_auth: %s", ex)
            db.session.rollback()  # pylint: disable=consider-using-transaction
